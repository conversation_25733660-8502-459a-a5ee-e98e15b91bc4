{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 106, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/testing/src/app/page.tsx"], "sourcesContent": ["import { ethers } from 'ethers';\nimport React, { useState, useEffect } from 'react';\n\nconst page = () => {\nconst address = '******************************************';\nconst provider = new ethers.EtherscanProvider('maticmum', '******************************************');\nconst balance = provider.getBalance(address);\nconsole.log(provider);\n\n\n\n\n\n}"], "names": [], "mappings": ";AAAA;;AAGA,MAAM,OAAO;IACb,MAAM,UAAU;IAChB,MAAM,WAAW,IAAI,gLAAA,CAAA,SAAM,CAAC,iBAAiB,CAAC,YAAY;IAC1D,MAAM,UAAU,SAAS,UAAU,CAAC;IACpC,QAAQ,GAAG,CAAC;AAMZ", "debugId": null}}]}